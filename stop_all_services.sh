#!/bin/bash

# MCP-CMS 停止所有服务器服务脚本
# 基于 transfer_fixed.sh 的服务停止功能

set -e

# 从配置文件加载服务器配置（如果存在）
load_config() {
    local config_file="${1:-server_config.conf}"
    if [ -f "$config_file" ]; then
        print_info "加载配置文件: $config_file"
        source "$config_file"
    fi
}

# 服务器配置函数
get_server() {
    case $1 in
        "qd") echo "${SERVER_QD:-<EMAIL>}";;
        "sha") echo "${SERVER_SHA:-<EMAIL>}";;
        "tyo") echo "${SERVER_TYO:-<EMAIL>}";;
        "hkg") echo "${SERVER_HKG:-<EMAIL>}";;
    esac
}

get_service_name() {
    case $1 in
        "qd") echo "${SERVICE_QD:-mcp-cms}";;
        "sha") echo "${SERVICE_SHA:-mcp-cms}";;
        "tyo") echo "${SERVICE_TYO:-mcp-cms}";;
        "hkg") echo "${SERVICE_HKG:-mcp-cms-hkg}";;
    esac
}

# 通用配置
PASSWORD="2929!lxj#LXJ"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 停止服务
stop_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 停止服务 $service_name..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name" 2>/dev/null; then
        print_success "[$server_key] 服务停止成功"
    else
        print_warning "[$server_key] 停止服务失败，可能服务不存在或已停止"
    fi
}

# 检查SSH连接
check_ssh_connection() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    
    print_info "[$server_key] 检查SSH连接到 $server..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$server" "exit" 2>/dev/null; then
        print_success "[$server_key] SSH连接成功"
        return 0
    else
        print_error "[$server_key] SSH连接失败"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [配置文件] [服务器...]"
    echo ""
    echo "参数："
    echo "  配置文件     可选的配置文件路径"
    echo "  服务器       可选的服务器列表 (qd sha tyo hkg)，如不指定则停止所有服务器"
    echo ""
    echo "选项："
    echo "  --help, -h   显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0                    # 停止所有服务器的服务"
    echo "  $0 qd sha             # 只停止 QD 和 SHA 服务器的服务"
    echo "  $0 config.conf hkg    # 使用配置文件并停止 HKG 服务器的服务"
    echo ""
}

# 主函数
main() {
    local config_file=""
    local target_servers=()
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            qd|sha|tyo|hkg)
                target_servers+=("$1")
                shift
                ;;
            *)
                if [[ -z "$config_file" ]]; then
                    if [[ -f "$1" ]]; then
                        config_file="$1"
                    elif [[ "$1" =~ ^[a-zA-Z0-9_.-]+$ ]]; then
                        config_file="$1"
                    fi
                fi
                shift
                ;;
        esac
    done
    
    load_config "$config_file"
    
    # 如果没有指定服务器，默认停止所有服务器
    if [ ${#target_servers[@]} -eq 0 ]; then
        target_servers=("qd" "sha" "tyo" "hkg")
    fi
    
    echo "🛑 开始停止 MCP-CMS 服务..."
    echo "🎯 目标服务器: ${target_servers[*]}"
    echo ""
    
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass未安装，请先安装: brew install sshpass"
        exit 1
    fi
    
    local failed_servers=()
    
    # 第一步：检查服务器连接
    print_info "第1步: 检查服务器连接..."
    for server_key in "${target_servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败: ${failed_servers[*]}"
        print_warning "将跳过连接失败的服务器，继续停止其他服务器的服务"
        
        # 从目标服务器列表中移除连接失败的服务器
        local temp_servers=()
        for server_key in "${target_servers[@]}"; do
            if [[ ! " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
                temp_servers+=("$server_key")
            fi
        done
        target_servers=("${temp_servers[@]}")
    fi
    
    if [ ${#target_servers[@]} -eq 0 ]; then
        print_error "没有可用的服务器，脚本退出"
        exit 1
    fi
    
    # 第二步：停止服务
    print_info "第2步: 停止服务..."
    for server_key in "${target_servers[@]}"; do
        stop_service "$server_key"
    done
    
    echo ""
    print_success "服务停止操作完成!"
    echo ""
    print_info "服务器状态:"
    for server_key in "${target_servers[@]}"; do
        echo "✅ [$server_key] $(get_server "$server_key") - 服务已停止"
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        echo ""
        print_warning "跳过的服务器:"
        for server_key in "${failed_servers[@]}"; do
            echo "❌ [$server_key] $(get_server "$server_key") - 连接失败"
        done
    fi
    echo ""
}

trap 'print_error "脚本执行被中断"; exit 1' INT TERM

main "$@"