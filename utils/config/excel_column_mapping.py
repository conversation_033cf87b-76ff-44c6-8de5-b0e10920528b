#!/usr/bin/env python3
"""
Excel列名映射配置
用于Job业务明细数据导出时的字段名映射
"""

# 完整的Job明细字段映射
JOB_DETAILS_COLUMN_MAPPING = {
    # 基本信息
    'job_file_id': 'Job文件ID',
    'job_file_no': 'Job编号',
    'business_type': '业务类型',
    'business_type_name': '业务类型名称',
    'job_date': 'Job日期',
    
    # 操作信息
    'operator_name': '操作员',
    'operator_dept_name': '操作部门',
    'job_handling_agent_name': '工作档代理',
    
    # 船期信息
    'vessel': '船名/航班',
    'voyage': '航次/航班日期',
    'etd_date': '开船/起飞日期',
    'eta_date': '到港日期',
    'pol_code': '起运港代码',
    'pod_code': '目的港代码',
    
    # 状态信息
    'is_checked': '是否审核',
    'is_op_finished': '操作是否完成',
    
    # 财务信息
    'income': '收入',
    'cost': '成本',
    'profit': '利润',
    
    # 票数统计
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    
    # 集拼信息
    'is_consol': '是否集拼',
    'consol_20_count': '集拼20\'数量',
    'consol_40_count': '集拼40\'数量',
    
    # 重量/体积统计
    'total_rt': '总RT',
    'total_teu': '总TEU',
    'total_air_weight': '总空运重量',
    
    # 转运统计
    'transhipment_count': '转运票数',
    'transhipment_rt': '转运RT',
    'transhipment_profit': '转运利润',
    
    # 指定货统计
    'all_nominated_count': '所有指定货票数',
    'all_nominated_rt': '所有指定货RT',
    'all_nominated_profit': '所有指定货利润',
    'port_agent_nominated_count': '港代指定货票数',
    'port_agent_nominated_rt': '港代指定货RT',
    'port_agent_nominated_profit': '港代指定货利润'
}

# 集拼统计汇总表字段映射
CONSOL_SUMMARY_COLUMN_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'consol_20_count': '集拼20\'数量',
    'consol_40_count': '集拼40\'数量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}

# 集拼明细表字段映射
CONSOL_DETAIL_COLUMN_MAPPING = {
    'job_file_no': 'Job编号',
    'business_type_name': '业务类型',
    'job_date': 'Job日期',
    'vessel': '船名/航班',
    'voyage': '航次/航班日期',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'consol_20_count': '集拼20\'数量',
    'consol_40_count': '集拼40\'数量',
    'income': '收入',
    'profit': '利润'
}

# 按业务类型统计表字段映射
BUSINESS_TYPE_SUMMARY_COLUMN_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'is_consol': '集拼Job数量',
    'consol_20_count': '集拼20\'总数',
    'consol_40_count': '集拼40\'总数',
    'total_rt': '总RT',
    'total_teu': '总TEU',
    'total_air_weight': '总空运重量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}

# 按操作部门统计表字段映射
DEPT_SUMMARY_COLUMN_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'is_consol': '集拼Job数量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}

# 字段分组定义（用于有序导出）
FIELD_GROUPS = {
    'basic_info': ['job_file_id', 'job_file_no', 'business_type', 'business_type_name', 'job_date'],
    'operation_info': ['operator_name', 'operator_dept_name', 'job_handling_agent_name'],
    'shipping_info': ['vessel', 'voyage', 'etd_date', 'eta_date', 'pol_code', 'pod_code'],
    'status_info': ['is_checked', 'is_op_finished'],
    'financial_info': ['income', 'cost', 'profit'],
    'count_info': ['bk_count', 'bl_count'],
    'consol_info': ['is_consol', 'consol_20_count', 'consol_40_count'],
    'weight_info': ['total_rt', 'total_teu', 'total_air_weight'],
    'transhipment_info': ['transhipment_count', 'transhipment_rt', 'transhipment_profit'],
    'nominated_info': ['all_nominated_count', 'all_nominated_rt', 'all_nominated_profit',
                      'port_agent_nominated_count', 'port_agent_nominated_rt', 'port_agent_nominated_profit']
}

# 获取有序的字段列表
def get_ordered_fields():
    """
    获取按分组排序的字段列表
    
    Returns:
        list: 有序的字段名列表
    """
    ordered_fields = []
    for group_fields in FIELD_GROUPS.values():
        ordered_fields.extend(group_fields)
    return ordered_fields

# 获取可用的字段映射
def get_available_mapping(df, mapping_dict):
    """
    根据DataFrame中存在的字段，获取可用的映射字典
    
    Args:
        df: pandas DataFrame
        mapping_dict: 字段映射字典
        
    Returns:
        dict: 可用的字段映射
    """
    available_mapping = {}
    for eng_col, chn_col in mapping_dict.items():
        if eng_col in df.columns:
            available_mapping[eng_col] = chn_col
    return available_mapping

# 应用字段映射
def apply_column_mapping(df, mapping_dict):
    """
    应用字段映射到DataFrame
    
    Args:
        df: pandas DataFrame
        mapping_dict: 字段映射字典
        
    Returns:
        pandas DataFrame: 应用映射后的DataFrame
    """
    available_mapping = get_available_mapping(df, mapping_dict)
    
    # 选择存在的字段
    available_fields = list(available_mapping.keys())
    result_df = df[available_fields].copy()
    
    # 应用映射
    result_df.rename(columns=available_mapping, inplace=True)
    
    return result_df

# 数据格式化函数
def format_excel_data(df):
    """
    格式化Excel导出数据
    
    Args:
        df: pandas DataFrame
        
    Returns:
        pandas DataFrame: 格式化后的DataFrame
    """
    import pandas as pd
    
    formatted_df = df.copy()
    
    for col in formatted_df.columns:
        if '日期' in col:
            # 日期格式化
            try:
                formatted_df[col] = pd.to_datetime(formatted_df[col]).dt.strftime('%Y-%m-%d')
            except:
                pass
        elif any(keyword in col for keyword in ['收入', '成本', '利润', 'RT', 'TEU', '重量']):
            # 数值格式化
            try:
                formatted_df[col] = pd.to_numeric(formatted_df[col], errors='coerce').fillna(0)
            except:
                pass
    
    return formatted_df

# 使用示例
if __name__ == "__main__":
    # 打印所有映射
    print("Job明细字段映射:")
    for eng, chn in JOB_DETAILS_COLUMN_MAPPING.items():
        print(f"  {eng} -> {chn}")
    
    print(f"\n总共 {len(JOB_DETAILS_COLUMN_MAPPING)} 个字段")
    
    print("\n字段分组:")
    for group_name, fields in FIELD_GROUPS.items():
        print(f"  {group_name}: {len(fields)} 个字段")
    
    print(f"\n有序字段列表: {get_ordered_fields()}")
