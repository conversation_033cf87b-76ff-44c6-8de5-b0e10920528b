# 将oss文件上传到dwz，并返回短链接

import os
import time
import urllib.parse
from io import BytesIO
import oss2
from . import dwz
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class OssClient:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OssClient, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        
        # 获取OSS配置
        access_key_id = os.getenv('OSS_ACCESS_KEY_ID')
        access_key_secret = os.getenv('OSS_ACCESS_KEY_SECRET')
        
        if not access_key_id or not access_key_secret:
            raise ValueError("OSS credentials not found. Please set OSS_ACCESS_KEY_ID and OSS_ACCESS_KEY_SECRET in .env file")
        
        # 使用Auth而不是ProviderAuthV4，更稳定
        self.auth = oss2.Auth(access_key_id, access_key_secret)
        self.endpoint = 'https://oss-cn-hangzhou.aliyuncs.com'  # 根据错误信息更正为杭州节点
        self.region = 'cn-hangzhou'  # 阿里云OSS region
        self.bucket_name = 'mcp-cms'  # 阿里云OSS bucket name
        
        # 创建Bucket对象
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name, region=self.region)
        self._initialized = True

    def upload_get_url(self, local_file):
        """
        上传文件到OSS并返回带签名的URL
        local_file: 可以是文件路径字符串或者文件对象(BytesIO)
        """
        try:
            # 生成文件名
            if isinstance(local_file, BytesIO):
                # 获取BytesIO对象的文件名，如果没有则根据内容推断
                original_filename = getattr(local_file, 'name', None)
                if not original_filename:
                    # 默认使用时间戳作为文件名，扩展名根据上下文推断
                    original_filename = f'file_{int(time.time())}.xlsx'
            else:
                original_filename = os.path.basename(local_file)
            
            # 构建OSS文件路径，确保文件扩展名正确，处理中文文件名
            # 获取文件扩展名
            file_ext = os.path.splitext(original_filename)[1]
            # 使用时间戳作为文件名主体，避免中文路径问题
            timestamp = int(time.time())
            safe_filename = f'file_{timestamp}{file_ext}'
            oss_file = f'temp_docs/{safe_filename}'
            
            # 设置正确的Content-Type，特别是对于CSV文件
            content_type = None
            if original_filename.lower().endswith('.csv'):
                content_type = 'text/csv'
            elif original_filename.lower().endswith('.xlsx'):
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            # 上传文件，使用正确的Content-Type
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
                # 注意：不在上传时设置Content-Disposition，因为这会影响所有访问
                # 而是在下载URL中设置，这样可以根据需要动态调整文件名
            
            if isinstance(local_file, (BytesIO, bytes)):
                self.bucket.put_object(oss_file, local_file, headers=headers if headers else None)
            else:
                self.bucket.put_object_from_file(oss_file, local_file, headers=headers if headers else None)
            
            # 生成带签名的URL，有效期7天
            # 只设置Content-Disposition，不覆盖Content-Type（OSS不允许）
            url_params = {}
            if original_filename.lower().endswith('.csv') or original_filename.lower().endswith('.xlsx'):
                encoded_filename = urllib.parse.quote(os.path.basename(original_filename), safe='')
                url_params['response-content-disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}'
                # 注意：不设置response-content-type，因为OSS不允许覆盖
                print(f"设置下载文件名: {original_filename} -> {encoded_filename}")
            
            url = self.bucket.sign_url('GET', oss_file, 7 * 24 * 3600, params=url_params if url_params else None)
            print(f"生成的OSS URL: {url[:100]}...")  # 只显示前100个字符
            return url
            
        except oss2.exceptions.ServerError as e:
            print(f"OSS Server Error: {str(e)}")
            raise
        except oss2.exceptions.RequestError as e:
            print(f"OSS Request Error: {str(e)}")
            raise
        except Exception as e:
            print(f"Unexpected Error: {str(e)}")
            raise

    def get_file_url(self, oss_file: str, img: bool = False):
        """
        获取文件的签名URL
        img: 如果是图片，可以添加图片处理参数
        """
        params = {'x-oss-process': 'image/format,jpg'} if img else None
        return self.bucket.sign_url('GET', oss_file, 7 * 24 * 3600, params=params)

def upload_get_dwz_url(local_file, need_dwz: bool = False):
    """
    上传文件并返回短链接
    local_file: 可以是文件路径字符串或者文件对象(BytesIO)
    """
    try:
        oss_client = OssClient()
        url = oss_client.upload_get_url(local_file)
        if need_dwz:
            client = dwz.Dwz('ca0bd1780baa4dc3e94d76032eebe5c5')
            short_url = client.create_single(url, dwz.TOV.ONE_YEAR)
            return short_url
        return url
    except Exception as e:
        print(f"Error uploading file: {str(e)}")
        raise
