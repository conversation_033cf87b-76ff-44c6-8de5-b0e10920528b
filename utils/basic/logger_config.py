"""
通用日志配置模块
提供统一的日志配置功能，便于其他模块调用
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Union, Literal

# 日志级别映射
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL
}

def setup_logger(
    name: str,
    log_file: Optional[Union[str, Path]] = None,
    level: Literal["debug", "info", "warning", "error", "critical"] = "warning",
    log_to_console: bool = True,
    log_to_file: bool = True,
    formatter: Optional[logging.Formatter] = None,
    propagate: bool = False
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 记录器名称，通常使用 __name__
        log_file: 日志文件路径，默认为None时使用模块名
        level: 日志级别，可选值为debug、info、warning、error、critical
        log_to_console: 是否记录到控制台
        log_to_file: 是否记录到文件
        formatter: 自定义格式化器
        propagate: 是否传播日志到上级logger
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 获取日志级别
    log_level = LOG_LEVELS.get(level.lower(), logging.WARNING)
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    logger.propagate = propagate
    
    # 如果已经有处理器，则先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 设置默认格式化器
    if formatter is None:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # 添加控制台处理器
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 添加文件处理器
    if log_to_file:
        # 确保logs目录存在
        logs_dir = Path('logs')
        logs_dir.mkdir(exist_ok=True)
        
        # 如果未提供日志文件名，则使用模块名
        if log_file is None:
            module_name = name.split('.')[-1]
            log_file = logs_dir / f"{module_name}.log"
        else:
            log_file = logs_dir / Path(log_file).name if isinstance(log_file, (str, Path)) else logs_dir / "app.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(
    name: str, 
    level: Literal["debug", "info", "warning", "error", "critical"] = "warning"
) -> logging.Logger:
    """
    获取已配置的日志记录器的简便方法
    
    Args:
        name: 记录器名称，通常使用 __name__
        level: 日志级别，默认为warning
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    return setup_logger(name, level=level) 