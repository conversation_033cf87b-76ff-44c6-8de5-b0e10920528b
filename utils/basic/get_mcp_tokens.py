# 获取MCP Client Token

import asyncio
import warnings

from utils.basic.data_conn_unified import execute_mysql_query_async

# 忽略ResourceWarning警告
warnings.filterwarnings("ignore", category=ResourceWarning)

async def get_mcp_tokens(token_uuid:str, database:str='mcp_tokens'):
    """
    检查给定的token是否存在于数据库中
    
    Args:
        token_uuid: 需要验证的token字符串
        database: 数据库名称
        
    Returns:
        如果token有效，返回token信息；否则返回None
    """
    
    try:
        result = await execute_mysql_query_async(
            query="SELECT token_uuid FROM tokens WHERE token_uuid = %s AND is_void = 0",
            params=(token_uuid,),
            database=database
        )
        # 如果找到有效token，返回结果
        if result:
            return result
        return None
    except Exception as e:
        return None

async def verify_mcp_token(token_uuid:str):
    """
    验证MCP Token是否有效
    
    Args:
        token_uuid: 需要验证的token字符串
        
    Returns:
        bool: 如果token有效返回True，否则返回False
    """
    try:
        # 获取MCP Client Token
        tokens = await get_mcp_tokens(token_uuid, database='mcp_tokens')
        if tokens:
            return True
        else:
            return False
    except Exception as e:
        return False

if __name__ == "__main__":
    # 使用一个有效的UUID格式字符串
    asyncio.run(verify_mcp_token(token_uuid='6660841e-54ad-4025-83d1-7226529fa670')) # 使用前面生成的一个实际存在的UUID

