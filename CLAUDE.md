# CLAUDE.md

这个文件为在此代码仓库中工作的 Claude Code (claude.ai/code) 提供指导。

## 常用开发命令

### 环境设置
```bash
# 安装 UV 包管理器（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate  # 或使用: uv shell
```

### 运行服务器
```bash
# 启动主 MCP 服务器（默认: http://0.0.0.0:8011）
python mcp_server_cms.py

# 使用自定义端口运行
MCP_CMS_PORT=8012 python mcp_server_cms.py

# 使用特定主机运行
HTTP_HOST=127.0.0.1 MCP_CMS_PORT=8011 python mcp_server_cms.py
```

### 测试和调试
```bash
# 测试利润数据调度器功能
python test_scheduler.py

# 手动触发数据分析（开发调试用）
# 修改 test_scheduler.py 中的日期范围进行测试
```

### 数据库连接测试
```bash
# 测试数据库连接
python utils/basic/connection_diagnostic.py
```

### 部署
```bash
# 部署到所有服务器（QD, SHA, TYO, HKG）
./transfer_to_server.sh

# 部署到特定服务器
./transfer_to_server.sh qd sha
```

## 架构概述

### 核心系统
这是一个为物流和货运代理业务构建的**企业内容管理系统**，作为 **MCP (Model Context Protocol) 服务器**运行。通过 FastAPI 和 SSE（服务器发送事件）通信提供 AI 驱动的数据导出和搜索功能。

### 技术栈
- **Python 3.12+** 配合 FastAPI 和 FastAPI-MCP
- **数据库层**: Firebird（主要）+ MySQL（辅助）配备连接池
- **数据处理**: Pandas 用于分析，OpenPyXL/ReportLab 用于导出
- **基础设施**: Uvicorn ASGI 服务器，Prometheus 监控，OSS2 云存储

### 核心组件

#### MCP 服务器 (`mcp_server_cms.py`)
- 具有自动环境检测的主要入口点
- 通过 `utils/basic/get_mcp_tokens.py` 进行基于令牌的身份验证
- 生产环境优化（更高并发，扩展超时）
- Prometheus 指标端口为 `HTTP_PORT + 100`

#### 数据库层 (`utils/database/` & `utils/basic/data_conn_unified.py`)
- 具有重试机制的统一连接管理
- SSH 隧道支持安全连接
- 连接池：最大 25 个 Firebird 连接，可配置超时
- `utils/basic/db_pro2_sea_air_profit.py` 中的业务逻辑查询

#### 导出系统 (`utils/basic/simple_excel_export.py`)
- Excel/CSV 导出功能与 OSS 云集成
- 具有分页和超时保护的大数据集处理
- 财务分析（收入、成本、利润计算）
- 物流数据多格式支持

#### FastAPI 应用 (`utils/fastapi_apps/fastapi_cms_simplified.py`)
- 简化的 CMS 界面
- 数据操作的 RESTful 端点
- 身份验证和错误处理中间件

### 核心功能

#### 可用的 MCP 工具
- `export_bookings` - 导出船运预订数据到 Excel/CSV
- `export_jobs` - 导出包含业务指标的工作订单/作业数据
- `unified_search` - 按部分名称搜索公司、人员、部门

#### 自动化数据分析系统
系统内置周期性利润数据分析功能，自动运行 `get_job_details_with_transhipment` 和 `get_sea_air_profit_with_transhipment` 函数：

**调度规则**：
- 执行时间：每周六、周日 07:00-24:00
- 分析周期：6个月数据块
- 频率分层：
  - 距今2年以上：每6个月检查一次
  - 距今1-2年：每3个月检查一次  
  - 距今6个月-1年：每1个月检查一次
  - 距今1-5个月：每1周检查一次
- 数据排除：当月数据（1-7日还排除上月）
- 异步处理：不同频率周期可并发执行，提高处理效率
- 检查时间跟踪：通过 `t_scheduler_check_log` 表记录各周期最后检查时间

**存储位置**：
- Job数据 → `mcp_tokens.t_job_details` 表
- Booking数据 → `mcp_tokens.t_booking_details` 表
- 支持变更检测，仅保存有变化的数据
- 每次分析使用唯一 session_id 进行数据隔离
- 通过 `pro2_system_id` 字段区分不同服务器/地区的数据

#### 业务数据模型
- **预订数据**: 海运/空运货运，包含收入/成本分析、船舶跟踪、销售员分配
- **作业数据**: 包含 TEU 计算、拼箱跟踪、审批工作流的工作订单
- **搜索实体**: 具有基于角色访问的公司、用户、部门

### 配置

#### 必需的环境变量
```bash
# 🌍 多服务器环境配置（必须）
PRO2_SYSTEM_ID=86532  # 系统ID，用于区分不同地区/服务器的数据

# 数据库连接
FIREBIRD_HOST=<主机>
FIREBIRD_USER=<用户>
FIREBIRD_PASSWORD=<密码>
FIREBIRD_DB_PATH=<路径>

MYSQL_HOST=<主机>
MYSQL_USER=<用户>
MYSQL_PASSWORD=<密码>

# SSH 隧道（如果需要）
SSH_HOST=<主机>
SSH_USER=<用户>
SSH_PASSWORD=<密码>

# 服务器设置
MCP_CMS_PORT=8011
HTTP_HOST=0.0.0.0
BASE_URL=http://127.0.0.1:8011
```

#### 多服务器配置
系统支持多地区部署，通过 `PRO2_SYSTEM_ID` 区分不同服务器的数据：

| 地区 | 系统ID | 配置文件示例 |
|------|--------|-------------|
| 青岛 (QD) | 86532 | `.env.qd.example` |
| 上海 (SHA) | 86533 | `.env.sha.example` |
| 东京 (TYO) | 86534 | `.env.tyo.example` |
| 香港 (HKG) | 86535 | `.env.hkg.example` |

#### 配置管理
- `config.py` 中的中央配置，支持环境变量覆盖
- 基于计划的执行，包含工作日特定时间范围
- 连接池设置和重试机制
- 运营的电子邮件通知支持

### 多服务器部署
系统部署在 4 个地理区域：
- **QD**: 青岛 (`qd.cmsgroup.com.cn`)
- **SHA**: 上海 (`sh.cmsgroup.com.cn`)
- **TYO**: 东京 (`jp-server.cmslogistics.info`)
- **HKG**: 香港 (`cmshk-aliyun.ddns.net`)

部署包括自动服务停止/启动、健康检查和每个环境的配置管理。

### 开发说明

#### 包管理
- 使用 **UV**（超快 Python 包管理器）而不是 pip
- `pyproject.toml` 中的依赖项配置中国镜像以加快安装
- 锁文件 `uv.lock` 确保可重现构建

#### 环境检测
- 基于主机名模式自动检测生产环境与开发环境
- 相应调整工作进程数、超时和连接池
- Linux 系统获得 uvloop 优化，其他系统使用标准 asyncio

#### 安全考虑
- 所有端点的 MCP 令牌身份验证
- 参数化查询防止 SQL 注入
- 数据库访问的 SSH 隧道
- 基于环境的配置以保护机密

#### 缺失的开发基础设施
- 无自动化测试套件（通过连接诊断手动测试）
- 无代码检查/格式化配置
- 无 CI/CD 管道设置
- 通过 shell 脚本手动部署