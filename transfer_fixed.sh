#!/bin/bash

# MCP-CMS 项目文件传输脚本 - 修复版
# 支持同时更新4个服务器，包含服务停止/启动功能和Python缓存清理

set -e

# 从配置文件加载服务器配置（如果存在）
load_config() {
    local config_file="${1:-server_config.conf}"
    if [ -f "$config_file" ]; then
        print_info "加载配置文件: $config_file"
        source "$config_file"
    fi
}

# 服务器配置函数
get_server() {
    case $1 in
        "qd") echo "${SERVER_QD:-<EMAIL>}";;
        "sha") echo "${SERVER_SHA:-<EMAIL>}";;
        "tyo") echo "${SERVER_TYO:-<EMAIL>}";;
        "hkg") echo "${SERVER_HKG:-<EMAIL>}";;
    esac
}

get_remote_path() {
    case $1 in
        "qd") echo "${REMOTE_PATH_QD:-/home/<USER>/mcp-cms/}";;
        "sha") echo "${REMOTE_PATH_SHA:-/home/<USER>/mcp-cms-sha/}";;
        "tyo") echo "${REMOTE_PATH_TYO:-/root/mcp-cms-tyo/}";;
        "hkg") echo "${REMOTE_PATH_HKG:-/root/mcp-cms-hkg/}";;
    esac
}

get_service_name() {
    case $1 in
        "qd") echo "${SERVICE_QD:-mcp-cms}";;
        "sha") echo "${SERVICE_SHA:-mcp-cms}";;
        "tyo") echo "${SERVICE_TYO:-mcp-cms}";;
        "hkg") echo "${SERVICE_HKG:-mcp-cms-hkg}";;
    esac
}

# 通用配置
LOCAL_PATH="/Volumes/PSSD/code_files/mcp-cms"
PASSWORD="2929!lxj#LXJ"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 停止服务
stop_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 停止服务 $service_name..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name" 2>/dev/null; then
        print_success "[$server_key] 服务停止成功"
    else
        print_warning "[$server_key] 停止服务失败，可能服务不存在或已停止"
    fi
}

# 启动服务
start_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 启动服务 $service_name..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl start $service_name" 2>/dev/null; then
        print_success "[$server_key] 服务启动命令执行成功"
    else
        print_error "[$server_key] 启动服务失败"
        return 1
    fi
    
    sleep 3
    
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl is-active $service_name" >/dev/null 2>&1; then
        print_success "[$server_key] 服务 $service_name 启动成功"
    else
        print_error "[$server_key] 服务 $service_name 启动失败"
        return 1
    fi
}

# 检查SSH连接
check_ssh_connection() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    
    print_info "[$server_key] 检查SSH连接到 $server..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$server" "exit" 2>/dev/null; then
        print_success "[$server_key] SSH连接成功"
        return 0
    else
        print_error "[$server_key] SSH连接失败"
        return 1
    fi
}

# 传输文件到单个服务器
transfer_to_server() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 开始传输文件到 $server..."
    
    # 创建远程目录结构
    print_info "[$server_key] 创建远程目录结构..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "mkdir -p $remote_path/{logs,utils/{basic,database,fastapi_apps}}" || {
        print_error "[$server_key] 创建远程目录失败"
        return 1
    }
    
    # 使用rsync传输文件
    print_info "[$server_key] 传输项目文件..."
    rsync -avz --progress \
        --exclude-from=/tmp/rsync_exclude \
        --delete \
        -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no" \
        "$LOCAL_PATH/" \
        "$server:$remote_path/" || {
        print_error "[$server_key] 文件传输失败"
        return 1
    }
    
    # 设置文件权限
    print_info "[$server_key] 设置文件权限..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "find $remote_path -name '*.py' -exec chmod +x {} \\; && chmod +x $remote_path/*.sh 2>/dev/null || true"
    
    # 清理Python缓存
    print_info "[$server_key] 清理Python缓存..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "find $remote_path -type d -name '__pycache__' -exec rm -rf {} \\; 2>/dev/null || true; \
         find $remote_path -name '*.pyc' -delete 2>/dev/null || true; \
         find $remote_path -name '*.pyo' -delete 2>/dev/null || true" 2>/dev/null || true
    
    print_success "[$server_key] 文件传输和缓存清理完成"
    return 0
}

# 清空测试数据表
clear_test_tables() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 清空测试数据表..."
    
    # 创建清空表的SQL脚本（使用直接数据库连接，不依赖.env文件）
    local clear_sql="
import sys
import pymysql

def clear_test_tables():
    print('🔍 开始清空测试数据表...')

    # 数据库连接配置（根据服务器调整）
    db_configs = {
        'qd': {
            'host': 'qd.cmsgroup.com.cn',
            'port': 33306,
            'user': 'victorlee',
            'password': '2929!lxj#LXJ',
            'database': 'mcp_tokens'
        },
        'sha': {
            'host': 'qd.cmsgroup.com.cn',  # SHA也使用QD的数据库
            'port': 33306,
            'user': 'victorlee',
            'password': '2929!lxj#LXJ',
            'database': 'mcp_tokens'
        },
        'tyo': {
            'host': 'qd.cmsgroup.com.cn',  # TYO也使用QD的数据库
            'port': 33306,
            'user': 'victorlee',
            'password': '2929!lxj#LXJ',
            'database': 'mcp_tokens'
        },
        'hkg': {
            'host': 'qd.cmsgroup.com.cn',  # HKG也使用QD的数据库
            'port': 33306,
            'user': 'victorlee',
            'password': '2929!lxj#LXJ',
            'database': 'mcp_tokens'
        }
    }

    # 获取服务器标识（从命令行参数或环境变量）
    server_key = sys.argv[1] if len(sys.argv) > 1 else 'qd'

    if server_key not in db_configs:
        print(f'❌ 未知的服务器标识: {server_key}')
        sys.exit(1)

    config = db_configs[server_key]
    print(f'📊 连接到数据库: {config[\"host\"]}:{config[\"port\"]}/{config[\"database\"]}')

    try:
        # 创建数据库连接
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 清空 job details 表
            cursor.execute('TRUNCATE TABLE t_job_details')
            print('✅ t_job_details 表已清空')

            # 清空 booking details 表
            cursor.execute('TRUNCATE TABLE t_booking_details')
            print('✅ t_booking_details 表已清空')

            # 清空 scheduler check log 表
            cursor.execute('TRUNCATE TABLE t_scheduler_check_log')
            print('✅ t_scheduler_check_log 表已清空')

            connection.commit()
            print('🎯 所有测试表清空完成！')

    except Exception as e:
        print(f'❌ 清空表失败: {e}')
        import traceback
        print('详细错误信息:')
        traceback.print_exc()
        sys.exit(1)
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == '__main__':
    clear_test_tables()
"
    
    # 将SQL脚本写入远程服务器
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "cat > $remote_path/clear_test_tables.py << 'EOF'
$clear_sql
EOF"
    
    # 执行清空脚本（不再依赖.env文件）
    print_info "[$server_key] 执行清空脚本..."
    local clear_output
    local exit_code=0

    # 使用 set +e 临时禁用错误退出，避免脚本因清空失败而退出
    set +e

    # 检查 uv 是否可用（尝试多种路径）
    print_info "[$server_key] 检查 uv 可用性..."
    uv_path=""

    # 尝试不同的 uv 路径和环境
    uv_check_commands=(
        "which uv"
        "~/.local/bin/uv --version"
        "source ~/.bashrc && which uv"
        "source ~/.profile && which uv"
        "export PATH=\$PATH:~/.local/bin && which uv"
    )

    for check_cmd in "${uv_check_commands[@]}"; do
        if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "$check_cmd" >/dev/null 2>&1; then
            # 提取实际的 uv 路径
            uv_path=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "$check_cmd" 2>/dev/null | head -1)
            if [[ "$uv_path" == *"uv"* ]]; then
                print_info "[$server_key] 找到 uv: $uv_path"
                break
            fi
        fi
    done

    # 如果没有找到 uv，尝试安装
    if [[ -z "$uv_path" ]]; then
        print_info "[$server_key] uv 未找到，尝试安装..."
        sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
            "curl -LsSf https://astral.sh/uv/install.sh | sh" >/dev/null 2>&1 || true

        # 重新检查
        if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "~/.local/bin/uv --version" >/dev/null 2>&1; then
            uv_path="~/.local/bin/uv"
            print_info "[$server_key] uv 安装成功: $uv_path"
        else
            print_warning "[$server_key] uv 安装失败，跳过清空表操作"
            set -e
            return 0
        fi
    fi

    # 使用找到的 uv 路径执行清空脚本
    print_info "[$server_key] 使用 $uv_path 执行清空脚本..."

    # 构建完整的执行命令
    if [[ "$uv_path" == "~/.local/bin/uv" ]]; then
        uv_cmd="~/.local/bin/uv run clear_test_tables.py $server_key"
    elif [[ "$uv_path" == *"/.local/bin/uv" ]]; then
        uv_cmd="$uv_path run clear_test_tables.py $server_key"
    else
        # 使用环境变量确保路径正确
        uv_cmd="export PATH=\$PATH:~/.local/bin && uv run clear_test_tables.py $server_key"
    fi

    clear_output=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "cd $remote_path && $uv_cmd 2>&1")
    exit_code=$?

    set -e  # 重新启用错误退出

    if [[ $exit_code -eq 0 ]]; then
        print_success "[$server_key] 测试数据表清空成功"
        echo "$clear_output" | grep -E "✅|🎯" || true
    else
        print_warning "[$server_key] 清空测试表失败"
        echo "错误详情："
        echo "$clear_output"
        print_info "[$server_key] 继续执行文件传输..."
    fi
    
    # 清理临时脚本
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "rm -f $remote_path/clear_test_tables.py" 2>/dev/null || true
}

# 创建排除文件列表
create_exclude_file() {
    cat > /tmp/rsync_exclude << EOF
*.env
.env.*
*.md
.venv/
__pycache__/
*/__pycache__/
**/__pycache__/
*.pyc
*.pyo
.git/
.gitignore
.DS_Store
node_modules/
.pytest_cache/
*.log
logs/*.log
socket
uv.lock
*.xlsx
*.csv
*.json
server_config.conf.example
AI_*.md
*_GUIDE.md
example_*.py
test_*.py
*_backup.py
debug_*.py
check_*.py
EOF
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [配置文件] [选项]"
    echo ""
    echo "选项："
    echo "  --clear-tables    清空共享测试数据表 (t_job_details, t_booking_details, t_scheduler_check_log)"
    echo "                    注意：只在一个服务器上执行一次，使用内置数据库配置，不依赖.env文件"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0                           # 正常传输文件"
    echo "  $0 --clear-tables            # 传输文件并清空测试表"
    echo "  $0 config.conf --clear-tables # 使用配置文件并清空测试表"
    echo ""
}

# 主传输函数
main() {
    local config_file=""
    local clear_tables=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clear-tables)
                clear_tables=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "$config_file" && -f "$1" ]]; then
                    config_file="$1"
                elif [[ -z "$config_file" ]]; then
                    config_file="$1"
                fi
                shift
                ;;
        esac
    done
    
    load_config "$config_file"
    
    echo "🚀 开始传输 MCP-CMS 项目到4个服务器..."
    echo "📍 本地路径: $LOCAL_PATH"
    if [[ "$clear_tables" == true ]]; then
        echo "🧹 将清空共享测试数据表（在所有服务停止后执行一次）"
    fi
    echo ""
    
    if [ ! -d "$LOCAL_PATH" ]; then
        print_error "本地路径不存在: $LOCAL_PATH"
        exit 1
    fi
    
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass未安装，请先安装: brew install sshpass"
        exit 1
    fi
    
    create_exclude_file
    
    local servers=("qd" "sha" "tyo" "hkg")
    local failed_servers=()
    
    # 第一步：检查所有服务器连接
    print_info "第1步: 检查所有服务器连接..."
    for server_key in "${servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败: ${failed_servers[*]}"
        print_error "请检查服务器地址和密码, 脚本退出"
        exit 1
    fi
    
    # 第二步：停止所有服务器上的服务
    print_info "第2步: 停止所有服务器上的服务..."
    for server_key in "${servers[@]}"; do
        stop_service "$server_key"
    done

    # 第三步：清空测试数据表（可选，在所有服务停止后执行）
    if [[ "$clear_tables" == true ]]; then
        print_info "第3步: 清空共享测试数据表（在所有服务停止后执行）..."

        # 选择第一个连接成功的服务器来执行清空操作
        local clear_server=""
        for server_key in "${servers[@]}"; do
            clear_server="$server_key"
            break
        done

        if [[ -n "$clear_server" ]]; then
            print_info "使用服务器 [$clear_server] 执行清空操作..."
            clear_test_tables "$clear_server"
        fi
        step_num_transfer="第4步"
        step_num_start="第5步"
    else
        step_num_transfer="第3步"
        step_num_start="第4步"
    fi

    # 传输文件到所有服务器
    print_info "$step_num_transfer: 并行传输文件到所有服务器..."
    local pids=()
    
    for server_key in "${servers[@]}"; do
        (
            if transfer_to_server "$server_key"; then
                echo "$server_key:SUCCESS" > "/tmp/transfer_${server_key}.result"
            else
                echo "$server_key:FAILED" > "/tmp/transfer_${server_key}.result"
            fi
        ) &
        pids+=($!)
    done
    
    print_info "等待所有传输完成..."
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    # 收集传输结果
    failed_servers=()
    for server_key in "${servers[@]}"; do
        if [ -f "/tmp/transfer_${server_key}.result" ]; then
            result=$(cat "/tmp/transfer_${server_key}.result")
            if [[ "$result" == *"FAILED"* ]]; then
                failed_servers+=("$server_key")
            fi
            rm -f "/tmp/transfer_${server_key}.result"
        else
            failed_servers+=("$server_key")
        fi
    done
    
    # 启动所有服务器上的服务
    print_info "$step_num_start: 启动所有服务器上的服务..."
    for server_key in "${servers[@]}"; do
        if [[ ! " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
            start_service "$server_key"
        else
            print_warning "[$server_key] 跳过启动服务（传输失败）"
        fi
    done
    
    rm -f /tmp/rsync_exclude /tmp/transfer_*.result
    
    echo ""
    if [ ${#failed_servers[@]} -eq 0 ]; then
        print_success "所有服务器更新完成!"
        echo ""
        print_info "所有服务器状态:"
        for server_key in "${servers[@]}"; do
            echo "✅ [$server_key] $(get_server "$server_key") - 更新成功"
        done
    else
        print_warning "部分服务器更新失败"
        echo ""
        print_info "服务器状态:"
        for server_key in "${servers[@]}"; do
            if [[ " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
                echo "❌ [$server_key] $(get_server "$server_key") - 更新失败"
            else
                echo "✅ [$server_key] $(get_server "$server_key") - 更新成功"
            fi
        done
    fi
    echo ""
}

trap 'print_error "脚本执行被中断"; rm -f /tmp/rsync_exclude; exit 1' INT TERM

main "$@"