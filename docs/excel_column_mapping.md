# Excel列名映射配置

本文档记录了Job业务明细数据导出到Excel时的英文字段名到中文列名的映射关系，用于统一导出格式和便于维护。

## 完整字段映射表

### 基本信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| job_file_id | Job文件ID | 系统内部Job唯一标识 |
| job_file_no | Job编号 | 用户可见的Job编号 |
| business_type | 业务类型 | 业务类型代码 |
| business_type_name | 业务类型名称 | 业务类型中文名称 |
| job_date | Job日期 | Job创建/处理日期 |

### 操作信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| operator_name | 操作员 | 负责操作的员工姓名 |
| operator_dept_name | 操作部门 | 操作员所属部门 |
| job_handling_agent_name | 工作档代理 | Job对应的处理代理 |

### 船期信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| vessel | 船名/航班 | 船舶名称或航班号 |
| voyage | 航次/航班日期 | 航次号或航班日期 |
| etd_date | 开船/起飞日期 | 预计离港/起飞时间 |
| eta_date | 到港日期 | 预计到港/到达时间 |
| pol_code | 起运港代码 | 起运港口代码 |
| pod_code | 目的港代码 | 目的港口代码 |

### 状态信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| is_checked | 是否审核 | Job是否已审核 |
| is_op_finished | 操作是否完成 | 操作流程是否完成 |

### 财务信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| income | 收入 | 总收入金额 |
| cost | 成本 | 总成本金额 |
| profit | 利润 | 利润金额（收入-成本） |

### 票数统计字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| bk_count | 订舱票数 | Booking数量（仅海运出口有值） |
| bl_count | 提单票数 | BL/AWB数量 |

### 集拼信息字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| is_consol | 是否集拼 | 是否为集拼业务（1=是，0=否） |
| consol_20_count | 集拼20'数量 | 集拼业务中20英尺容器数量 |
| consol_40_count | 集拼40'数量 | 集拼业务中40英尺容器数量 |

### 重量/体积统计字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| total_rt | 总RT | 总收入吨位 |
| total_teu | 总TEU | 总标准箱数量 |
| total_air_weight | 总空运重量 | 空运货物总重量 |

### 转运统计字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| transhipment_count | 转运票数 | 转运业务票数 |
| transhipment_rt | 转运RT | 转运业务RT |
| transhipment_profit | 转运利润 | 转运业务利润 |

### 指定货统计字段
| 英文字段名 | 中文列名 | 说明 |
|-----------|---------|------|
| all_nominated_count | 所有指定货票数 | 所有指定货业务票数 |
| all_nominated_rt | 所有指定货RT | 所有指定货业务RT |
| all_nominated_profit | 所有指定货利润 | 所有指定货业务利润 |
| port_agent_nominated_count | 港代指定货票数 | 港代指定货业务票数 |
| port_agent_nominated_rt | 港代指定货RT | 港代指定货业务RT |
| port_agent_nominated_profit | 港代指定货利润 | 港代指定货业务利润 |

## 使用示例

### Python代码中的映射配置
```python
# 完整的字段映射字典
EXCEL_COLUMN_MAPPING = {
    # 基本信息
    'job_file_id': 'Job文件ID',
    'job_file_no': 'Job编号',
    'business_type': '业务类型',
    'business_type_name': '业务类型名称',
    'job_date': 'Job日期',
    
    # 操作信息
    'operator_name': '操作员',
    'operator_dept_name': '操作部门',
    'job_handling_agent_name': '工作档代理',
    
    # 船期信息
    'vessel': '船名/航班',
    'voyage': '航次/航班日期',
    'etd_date': '开船/起飞日期',
    'eta_date': '到港日期',
    'pol_code': '起运港代码',
    'pod_code': '目的港代码',
    
    # 状态信息
    'is_checked': '是否审核',
    'is_op_finished': '操作是否完成',
    
    # 财务信息
    'income': '收入',
    'cost': '成本',
    'profit': '利润',
    
    # 票数统计
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    
    # 集拼信息
    'is_consol': '是否集拼',
    'consol_20_count': '集拼20\'数量',
    'consol_40_count': '集拼40\'数量',
    
    # 重量/体积统计
    'total_rt': '总RT',
    'total_teu': '总TEU',
    'total_air_weight': '总空运重量',
    
    # 转运统计
    'transhipment_count': '转运票数',
    'transhipment_rt': '转运RT',
    'transhipment_profit': '转运利润',
    
    # 指定货统计
    'all_nominated_count': '所有指定货票数',
    'all_nominated_rt': '所有指定货RT',
    'all_nominated_profit': '所有指定货利润',
    'port_agent_nominated_count': '港代指定货票数',
    'port_agent_nominated_rt': '港代指定货RT',
    'port_agent_nominated_profit': '港代指定货利润'
}
```

### 使用方法
```python
# 在导出Excel时使用
export_df.rename(columns=EXCEL_COLUMN_MAPPING, inplace=True)
```

## 统计汇总表映射

### 集拼统计汇总表
```python
CONSOL_SUMMARY_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'consol_20_count': '集拼20\'数量',
    'consol_40_count': '集拼40\'数量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}
```

### 业务类型统计表
```python
BUSINESS_TYPE_SUMMARY_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'is_consol': '集拼Job数量',
    'consol_20_count': '集拼20\'总数',
    'consol_40_count': '集拼40\'总数',
    'total_rt': '总RT',
    'total_teu': '总TEU',
    'total_air_weight': '总空运重量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}
```

### 操作部门统计表
```python
DEPT_SUMMARY_MAPPING = {
    'job_file_id': 'Job数量',
    'bk_count': '订舱票数',
    'bl_count': '提单票数',
    'is_consol': '集拼Job数量',
    'income': '总收入',
    'cost': '总成本',
    'profit': '总利润'
}
```

## 注意事项

1. **字段顺序**: 建议按照上述分类顺序排列字段，便于用户查看
2. **数据格式**: 
   - 日期字段统一格式为 'YYYY-MM-DD'
   - 数值字段保留2位小数
   - 布尔字段使用 1/0 表示
3. **缺失字段**: 如果某些字段在数据中不存在，会自动跳过
4. **扩展性**: 新增字段时，请同时更新此映射表

## 更新历史

- 2025-01-07: 初始版本，包含完整的Job明细字段映射
- 2025-01-07: 新增集拼容器数量字段 (consol_20_count, consol_40_count)
