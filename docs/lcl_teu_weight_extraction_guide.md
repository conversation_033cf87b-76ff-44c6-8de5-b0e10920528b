# 海运出口/进口/三角贸易/空运 LCL RT/TEU/计费重量数据提取指南

## 概述

本文档介绍如何使用单独的SQL查询提取海运出口、海运进口、海运三角贸易和空运的LCL RT、TEU和计费重量数据。所有查询都输出统一格式，包含`lcl_rt`、`teu`、`air_weight`字段。

## 功能特点

- **统一输出格式**：所有业务类型都包含lcl_rt、teu、air_weight字段
- **字段合并**：按照用户偏好合并相关字段（bk_no/bl_no/awb_no → business_no，flight_no → vessel等）
- **SQL直接实现**：字段统一和合并在SQL查询中直接完成
- **编码处理**：正确处理GBK编码的业务号码字段
- **参数化查询**：支持时间范围过滤

## 数据字段说明

### 统一输出字段

| 字段名 | 类型 | 说明 | 海运出口 | 海运进口 | 海运三角 | 空运 |
|--------|------|------|----------|----------|----------|------|
| entity_id | string | 实体ID | booking_id | job_file_id_bl_id | bl_id | awb_id |
| job_file_id | int | 工作档ID | ✓ | ✓ | ✓ | ✓ |
| business_no | string | 业务号码 | booking_no | bl_no | bl_no | house_no |
| vessel | string | 船名/航班号 | vessel | vessel | vessel | flight_no |
| voyage | string | 航次/航班日期 | voyage | voyage | voyage | flight_date |
| etd_date | date | 开船/起飞日期 | ✓ | ✓ | ✓ | ✓ |
| eta_date | date | 到港/到达日期 | ✓ | ✓ | ✓ | ✓ |
| pol_code | string | 起运港代码 | ✓ | ✓ | ✓ | ✓ |
| pod_code | string | 目的港代码 | ✓ | NULL | ✓ | ✓ |
| lcl_rt | float | LCL计费吨 | maxvalue(cbm,kgs/1000,1) | maxvalue(cbm,kgs/1000,1) | maxvalue(cbm,kgs/1000,1) | chargeable_kgs或gross_kgs |
| teu | float | TEU数量 | 容器尺寸计算 | 容器尺寸计算 | 容器尺寸计算 | 0 |
| air_weight | float | 空运计费重量 | NULL | NULL | NULL | 等于lcl_rt |
| service_mode | int | 服务模式 | 1/2/3/0 | 1/2/3/0 | 原值 | 4 |
| business_type | string | 业务类型 | sea_export | sea_import | sea_triangle | air |

### 服务模式映射

- **海运出口/进口**：
  - 1: LCL (CFS/CFS)
  - 2: FCL (CY/CY)  
  - 3: BUY-CONSOL (CFS/CY)
  - 0: 其他

- **空运**：
  - 4: AIR

## SQL查询说明

### 1. 海运出口 LCL RT/TEU 提取

```sql
-- 文件：utils/basic/db_pro2_lcl_teu_weight_extraction.py
-- 函数：extract_sea_export_lcl_teu()
-- 时间字段：job_date
-- 主要表：sea_export_booking, sea_export_job_file
```

**特点**：
- LCL RT基于sea_export_bk_cfs_tonnage表计算
- TEU基于容器尺寸计算（20尺=1TEU，40尺=2TEU）
- 支持ETA日期从schedule表获取
- 业务号码为booking_no

### 2. 海运进口 LCL RT/TEU 提取

```sql
-- 文件：utils/basic/db_pro2_lcl_teu_weight_extraction.py  
-- 函数：extract_sea_import_lcl_teu()
-- 时间字段：eta_date
-- 主要表：sea_import_bl, sea_import_job_file
```

**特点**：
- LCL RT基于sea_import_bl_container表计算
- TEU基于容器尺寸计算，只计算FCL（is_cfs=0）
- 服务模式基于service_mode_from和service_mode_to计算
- 业务号码为bl_no

### 3. 海运三角贸易 LCL RT/TEU 提取

```sql
-- 文件：utils/basic/db_pro2_lcl_teu_weight_extraction.py
-- 函数：extract_sea_triangle_lcl_teu()  
-- 时间字段：job_date
-- 主要表：sea_switch_bl, sea_switch_job_file
```

**特点**：
- LCL RT基于sea_switch_bl_container表计算
- TEU基于容器尺寸计算，只计算FCL（is_cfs=0）
- 使用原始service_mode值
- 业务号码为bl_no

### 4. 空运 LCL RT/计费重量 提取

```sql
-- 文件：utils/basic/db_pro2_lcl_teu_weight_extraction.py
-- 函数：extract_air_lcl_weight()
-- 时间字段：flight_date  
-- 主要表：air_waybill, air_job_file
```

**特点**：
- LCL RT优先使用chargeable_kgs，否则使用gross_kgs
- TEU固定为0（空运无TEU概念）
- air_weight等于lcl_rt
- 服务模式固定为4
- 业务号码为house_no

## 使用方法

### 1. 单独提取某个业务类型

```python
from utils.basic.db_pro2_lcl_teu_weight_extraction import extract_sea_export_lcl_teu

# 提取海运出口数据
data = extract_sea_export_lcl_teu('2024-01-01', '2024-01-31')
for item in data:
    print(f"Entity: {item['entity_id']}, LCL RT: {item['lcl_rt']}, TEU: {item['teu']}")
```

### 2. 一次性提取所有业务类型

```python
from utils.basic.db_pro2_lcl_teu_weight_extraction import extract_all_lcl_teu_weight

# 提取所有数据
all_data = extract_all_lcl_teu_weight('2024-01-01', '2024-01-31')

# 按业务类型访问
sea_export_data = all_data['sea_export']
sea_import_data = all_data['sea_import'] 
sea_triangle_data = all_data['sea_triangle']
air_data = all_data['air']
```

### 3. 获取汇总统计

```python
from utils.basic.db_pro2_lcl_teu_weight_extraction import get_summary_statistics

# 获取统计信息
summary = get_summary_statistics(all_data)
print(f"海运出口总LCL RT: {summary['sea_export']['total_lcl_rt']}")
print(f"空运总计费重量: {summary['air']['total_air_weight']}")
```

## 运行示例

```bash
# 运行示例脚本
python examples/lcl_teu_weight_extraction_example.py

# 显示SQL查询语句
python examples/lcl_teu_weight_extraction_example.py --show-sql

# 指定日期范围
python examples/lcl_teu_weight_extraction_example.py --begin-date 2024-01-01 --end-date 2024-01-31
```

## 注意事项

1. **编码问题**：业务号码字段可能包含GBK编码的中文字符，查询时使用UTF-8字符集
2. **时间字段**：不同业务类型使用不同的时间字段进行过滤
3. **数据完整性**：某些字段可能为NULL，已在SQL中使用COALESCE处理
4. **性能考虑**：大时间范围查询可能较慢，建议分批处理
5. **TEU计算**：只有FCL货物才计算TEU，LCL货物TEU为0

## 扩展功能

如需添加其他字段或修改计算逻辑，可以：

1. 修改对应的SQL查询语句
2. 更新提取函数中的字段映射
3. 调整统一格式转换函数
4. 更新示例和文档

## 相关文件

- `utils/basic/db_pro2_lcl_teu_weight_extraction.py` - 主要功能模块
- `examples/lcl_teu_weight_extraction_example.py` - 使用示例
- `docs/lcl_teu_weight_extraction_guide.md` - 本文档
