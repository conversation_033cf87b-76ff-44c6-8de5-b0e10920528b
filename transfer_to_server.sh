#!/bin/bash

# MCP-CMS 项目文件传输脚本 - 多服务器版
# 支持同时更新4个服务器，包含服务停止/启动功能和Python缓存清理
# 服务器密码统一：2929!lxj#LXJ
#
# 使用方法：
#   ./transfer_to_server.sh          # 完整传输（包含缓存清理）
#   ./transfer_to_server.sh -c       # 仅清理缓存
#   ./transfer_to_server.sh --clean-cache-only  # 仅清理缓存
#
# 新增功能：
# - 自动清理远程服务器Python缓存（__pycache__、*.pyc、*.pyo）
# - 排除调试脚本传输
# - 支持仅缓存清理模式

set -e  # 遇到错误立即退出

# 从配置文件加载服务器配置（如果存在）
load_config() {
    local config_file="${1:-server_config.conf}"
    if [ -f "$config_file" ]; then
        print_info "加载配置文件: $config_file"
        source "$config_file"
    fi
}

# 服务器配置函数（可被配置文件覆盖）
get_server() {
    case $1 in
        "qd") echo "${SERVER_QD:-<EMAIL>}";;
        "sha") echo "${SERVER_SHA:-<EMAIL>}";;
        "tyo") echo "${SERVER_TYO:-<EMAIL>}";;
        "hkg") echo "${SERVER_HKG:-<EMAIL>}";;
    esac
}

get_remote_path() {
    case $1 in
        "qd") echo "${REMOTE_PATH_QD:-/home/<USER>/mcp-cms/}";;
        "sha") echo "${REMOTE_PATH_SHA:-/home/<USER>/mcp-cms-sha/}";;
        "tyo") echo "${REMOTE_PATH_TYO:-/root/mcp-cms-tyo/}";;
        "hkg") echo "${REMOTE_PATH_HKG:-/root/mcp-cms-hkg/}";;
    esac
}

get_service_name() {
    case $1 in
        "qd") echo "${SERVICE_QD:-mcp-cms}";;
        "sha") echo "${SERVICE_SHA:-mcp-cms}";;
        "tyo") echo "${SERVICE_TYO:-mcp-cms}";;
        "hkg") echo "${SERVICE_HKG:-mcp-cms-hkg}";;
    esac
}

# 通用配置
LOCAL_PATH="/Volumes/PSSD/code_files/mcp-cms"
PASSWORD="2929!lxj#LXJ"
SSH_KEY_PATH="$HOME/.ssh/id_rsa"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 停止服务
stop_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 停止服务 $service_name..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name" 2>/dev/null; then
        print_success "[$server_key] 服务停止成功"
    else
        print_warning "[$server_key] 停止服务失败，可能服务不存在或已停止"
    fi
}

# 清理Python缓存
clean_python_cache() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 清理Python缓存..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "find $remote_path -type d -name '__pycache__' -exec rm -rf {} \\; 2>/dev/null || true; \
         find $remote_path -name '*.pyc' -delete 2>/dev/null || true; \
         find $remote_path -name '*.pyo' -delete 2>/dev/null || true" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_success "[$server_key] Python缓存清理完成"
    else
        print_warning "[$server_key] Python缓存清理部分失败（正常情况）"
    fi
}

# 启动服务
start_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 启动服务 $service_name..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl start $service_name" 2>/dev/null; then
        print_success "[$server_key] 服务启动命令执行成功"
    else
        print_error "[$server_key] 启动服务失败"
        return 1
    fi
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl is-active $service_name" >/dev/null 2>&1; then
        print_success "[$server_key] 服务 $service_name 启动成功"
    else
        print_error "[$server_key] 服务 $service_name 启动失败"
        return 1
    fi
}

# 检查SSH连接
check_ssh_connection() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    
    print_info "[$server_key] 检查SSH连接到 $server..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$server" "exit" 2>/dev/null; then
        print_success "[$server_key] SSH连接成功"
        return 0
    else
        print_error "[$server_key] SSH连接失败"
        return 1
    fi
}

# 传输文件到单个服务器
transfer_to_server() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 开始传输文件到 $server..."
    
    # 创建远程目录结构
    print_info "[$server_key] 创建远程目录结构..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "mkdir -p $remote_path/{logs,utils/{basic,database,fastapi_apps}}" || {
        print_error "[$server_key] 创建远程目录失败"
        return 1
    }
    
    # 使用rsync传输文件
    print_info "[$server_key] 传输项目文件..."
    rsync -avz --progress \
        --exclude-from=/tmp/rsync_exclude \
        --delete \
        -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no" \
        "$LOCAL_PATH/" \
        "$server:$remote_path/" || {
        print_error "[$server_key] 文件传输失败"
        return 1
    }
    
    # 设置文件权限
    print_info "[$server_key] 设置文件权限..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "find $remote_path -name '*.py' -exec chmod +x {} \\; && chmod +x $remote_path/*.sh 2>/dev/null || true"
    
    print_success "[$server_key] 文件传输完成"
    return 0
}

# 创建排除文件列表
create_exclude_file() {
    cat > /tmp/rsync_exclude << EOF
.env
.env.*
*.md
.venv/
__pycache__/
*/__pycache__/
**/__pycache__/
*.pyc
*.pyo
.git/
.gitignore
.DS_Store
node_modules/
.pytest_cache/
*.log
logs/*.log
socket
uv.lock
*.xlsx
*.csv
*.json
server_config.conf.example
AI_*.md
*_GUIDE.md
example_*.py
test_*.py
*_backup.py
debug_*.py
check_*.py
EOF
}

# 只清理缓存（不传输文件）
clean_cache_only() {
    echo "🧹 开始清理所有服务器的Python缓存..."
    echo ""
    
    # 定义服务器列表
    local servers=("qd" "sha" "tyo" "hkg")
    local failed_servers=()
    
    # 检查所有服务器连接
    print_info "第1步: 检查所有服务器连接...")
    for server_key in "${servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败：${failed_servers[*]}"
        print_error "请检查服务器地址和密码，脚本退出"
        exit 1
    fi
    
    # 清理Python缓存
    print_info "第2步: 清理所有服务器的Python缓存..."
    for server_key in "${servers[@]}"; do
        clean_python_cache "$server_key"
    done
    
    print_success "所有服务器Python缓存清理完成！"
}

# 主传输函数
main() {
    # 检查命令行参数
    if [ "$1" = "--clean-cache-only" ] || [ "$1" = "-c" ]; then
        clean_cache_only
        return 0
    fi
    
    # 加载配置文件
    load_config "$1"
    
    echo "🚀 开始传输 MCP-CMS 项目到4个服务器..."
    echo "📍 本地路径：$LOCAL_PATH"
    echo ""
    
    # 检查本地路径是否存在
    if [ ! -d "$LOCAL_PATH" ]; then
        print_error "本地路径不存在：$LOCAL_PATH"
        exit 1
    fi
    
    # 检查sshpass是否安装
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass未安装，请先安装: brew install sshpass"
        exit 1
    fi
    
    # 创建排除文件
    create_exclude_file
    
    # 定义服务器列表
    local servers=("qd" "sha" "tyo" "hkg")
    local failed_servers=()
    
    # 第一步：检查所有服务器连接
    print_info "第1步: 检查所有服务器连接..."
    for server_key in "${servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败：${failed_servers[*]}"
        print_error "请检查服务器地址和密码，脚本退出"
        exit 1
    fi
    
    # 第二步：停止所有服务器上的服务
    print_info "第2步: 停止所有服务器上的服务..."
    for server_key in "${servers[@]}"; do
        stop_service "$server_key"
    done
    
    # 第三步：并行传输文件到所有服务器
    print_info "第3步: 并行传输文件到所有服务器..."
    local pids=()
    local transfer_results=()
    
    # 启动并行传输进程
    for server_key in "${servers[@]}"; do
        (
            if transfer_to_server "$server_key"; then
                echo "$server_key:SUCCESS" > "/tmp/transfer_${server_key}.result"
            else
                echo "$server_key:FAILED" > "/tmp/transfer_${server_key}.result"
            fi
        ) &
        pids+=($!)
    done
    
    # 等待所有传输完成
    print_info "等待所有传输完成..."
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    # 收集传输结果
    for server_key in "${servers[@]}"; do
        if [ -f "/tmp/transfer_${server_key}.result" ]; then
            result=$(cat "/tmp/transfer_${server_key}.result")
            if [[ "$result" == *"FAILED"* ]]; then
                failed_servers+=("$server_key")
            fi
            rm -f "/tmp/transfer_${server_key}.result"
        else
            failed_servers+=("$server_key")
        fi
    done
    
    # 第四步：清理Python缓存
    print_info "第4步: 清理所有服务器的Python缓存..."
    for server_key in "${servers[@]}"; do
        if [[ ! " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
            clean_python_cache "$server_key"
        else
            print_warning "[$server_key] 跳过缓存清理（传输失败）"
        fi
    done
    
    # 第五步：启动所有服务器上的服务
    print_info "第5步: 启动所有服务器上的服务..."
    for server_key in "${servers[@]}"; do
        if [[ ! " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
            start_service "$server_key"
        else
            print_warning "[$server_key] 跳过启动服务（传输失败）"
        fi
    done
    
    # 清理临时文件
    rm -f /tmp/rsync_exclude /tmp/transfer_*.result
    
    # 显示最终结果
    echo ""
    if [ ${#failed_servers[@]} -eq 0 ]; then
        print_success "所有服务器更新完成！"
        echo ""
        print_info "所有服务器状态："
        for server_key in "${servers[@]}"; do
            echo "✅ [$server_key] $(get_server "$server_key") - 更新成功"
        done
    else
        print_warning "部分服务器更新失败"
        echo ""
        print_info "服务器状态："
        for server_key in "${servers[@]}"; do
            if [[ " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
                echo "❌ [$server_key] $(get_server "$server_key") - 更新失败"
            else
                echo "✅ [$server_key] $(get_server "$server_key") - 更新成功"
            fi
        done
    fi
    echo ""
}

# 错误处理
trap 'print_error "脚本执行被中断"; rm -f /tmp/rsync_exclude; exit 1' INT TERM

# 执行主函数
main "$@" 