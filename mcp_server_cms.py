#!/usr/bin/env python3
"""
CMS企业管理系统 MCP Server - 全功能优化版本
集成搜索、数据导出、AI分析、系统监控等完整功能
支持前端AI代码生成和后端安全执行
"""

import os
import multiprocessing
import platform
import socket
import asyncio
import signal
from datetime import datetime
import time
import warnings
import logging
from fastapi_mcp import FastApiMCP, AuthConfig
from fastapi import Depends, HTTPException, Request
import uvicorn
from prometheus_client import start_http_server
from dotenv import load_dotenv
import threading

# 导入精简版FastAPI应用
from utils.fastapi_apps.fastapi_cms_simplified import app as fastapi_cms_final_app
from utils.basic.logger_config import setup_logger
from utils.basic.get_mcp_tokens import verify_mcp_token
from utils.basic.profit_data_scheduler import ProfitDataScheduler

# 过滤OSS2库的SyntaxWarning
warnings.filterwarnings("ignore", category=SyntaxWarning, module="oss2")
warnings.filterwarnings("ignore", message="invalid escape sequence", category=SyntaxWarning)

# 抑制无害的连接错误日志
class CustomUvicornFilter(logging.Filter):
    def filter(self, record):
        # 过滤掉ExceptionGroup和AssertionError相关的无害错误
        if "ExceptionGroup" in str(record.getMessage()):
            return False
        if "AssertionError" in str(record.getMessage()) and "http.response.body" in str(record.getMessage()):
            return False
        if "unhandled errors in a TaskGroup" in str(record.getMessage()):
            return False
        return True

# 应用过滤器到uvicorn相关的日志记录器
logging.getLogger("uvicorn.error").addFilter(CustomUvicornFilter())
logging.getLogger("uvicorn").addFilter(CustomUvicornFilter())

load_dotenv(override=True)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
    )

# 根据CPU核心数确定worker数量
CPU_CORES = multiprocessing.cpu_count()
if CPU_CORES == 1:
    WORKERS = 1
elif CPU_CORES <= 4:
    WORKERS = CPU_CORES * 2 -1
else:
    WORKERS = CPU_CORES + 1
WORKERS = min(WORKERS, 8)

# 获取环境变量或使用默认值
HTTP_HOST = os.getenv('HTTP_HOST', '0.0.0.0')
HTTP_PORT = int(os.getenv('MCP_CMS_PORT', 8011))
BASE_URL = os.getenv('BASE_URL', f'http://127.0.0.1:{HTTP_PORT}')

# 记录系统信息
logger.warning(f"系统信息: {platform.platform()}, Python: {platform.python_version()}")
logger.warning(f"CPU核心数: {CPU_CORES}, 配置的Worker数: {WORKERS}")
logger.warning(f"主机名: {socket.gethostname()}, IP: {socket.gethostbyname(socket.gethostname())}")
logger.warning(f"服务监听: {HTTP_HOST}:{HTTP_PORT}, 基础URL: {BASE_URL}")

# 检测服务器环境并优化配置
def detect_server_environment():
    """检测服务器环境并返回优化配置"""
    hostname = socket.gethostname()
    platform_info = platform.platform().lower()
    
    # 服务器环境检测
    is_server = any(x in hostname.lower() for x in ['server', 'prod', 'ubuntu', 'centos', 'debian'])
    is_linux = 'linux' in platform_info
    is_remote = not any(x in hostname.lower() for x in ['mac', 'local', 'dev'])
    
    server_env = is_server or (is_linux and is_remote)
    
    if server_env:
        logger.warning("检测到服务器环境，应用服务器优化配置")
        return {
            'limit_concurrency': 200,
            'timeout_keep_alive': 600,  # 延长到600秒支持长时间数据库查询
            'backlog': 512,
            'timeout_graceful_shutdown': 60,
            'h11_max_incomplete_event_size': 131072,  # 128KB
        }
    else:
        logger.warning("检测到本地开发环境，使用标准配置")
        return {
            'limit_concurrency': 100,
            'timeout_keep_alive': 600,  # 延长到600秒支持长时间数据库查询
            'backlog': 256,
            'timeout_graceful_shutdown': 30,
            'h11_max_incomplete_event_size': 65536,  # 64KB
        }

# 获取环境优化配置
server_config = detect_server_environment()

# MCP Token验证
async def verify_mcp_access(request: Request) -> dict:
    """验证MCP访问token"""
    token = None
    
    # 从Authorization头获取token
    auth_header = request.headers.get("authorization", "")
    if auth_header.startswith("Bearer "):
        token = auth_header[7:]
    
    # 从查询参数获取token
    if not token:
        token = request.query_params.get("mcp_token")
    
    if not token:
        logger.error("未提供MCP访问token")
        raise HTTPException(
            status_code=401,
            detail="需要提供有效的MCP访问token。请在Authorization头中提供Bearer token或在查询参数中提供mcp_token。"
        )
    
    # 验证token
    try:
        is_valid = await verify_mcp_token(token)
        if not is_valid:
            logger.error(f"无效的MCP token: {token[:10]}...")
            raise HTTPException(
                status_code=403,
                detail="提供的MCP token无效或已过期"
            )
        
        logger.info(f"MCP token验证成功: {token[:10]}...")
        return {"token": token, "valid": True}
        
    except Exception as e:
        logger.error(f"验证MCP token时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"验证MCP token时发生错误: {str(e)}"
        )

# 创建auth配置
auth_config = AuthConfig(
    dependencies=[Depends(verify_mcp_access)],
)

# 添加MCP错误处理中间件
async def mcp_error_handler(request, call_next):
    """处理MCP协议相关错误"""
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        import traceback
        logger.error(f"MCP服务处理错误: {e}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        
        error_msg = str(e)
        
        # 处理MCP初始化错误
        if "Received request before initialization was complete" in error_msg:
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=503,
                content={"detail": "MCP server is still initializing, please retry in a moment"},
                headers={
                    "Content-Type": "application/json",
                    "Retry-After": "2"
                }
            )
        
        # 处理客户端断开连接错误
        elif "ClientDisconnect" in error_msg or "starlette.requests.ClientDisconnect" in str(type(e)):
            logger.info("客户端断开连接，这是正常行为")
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=499,
                content={"detail": "Client disconnected"},
                headers={"Content-Type": "application/json"}
            )
        
        # 处理Starlette AssertionError (SSE相关错误)
        elif isinstance(e, AssertionError) and ('http.response.body' in error_msg or 'message["type"]' in error_msg):
            logger.info("检测到SSE响应格式错误，通常是客户端断开连接")
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=499,
                content={"detail": "Client disconnected during SSE"},
                headers={"Content-Type": "application/json"}
            )
        
        # 处理ExceptionGroup（Python 3.11+的异常组）
        elif hasattr(e, 'exceptions') or 'ExceptionGroup' in str(type(e)):
            logger.info("检测到ExceptionGroup，通常是客户端断开连接导致")
            
            # 检查异常组中的特定错误
            exceptions_to_check = []
            if hasattr(e, 'exceptions'):
                exceptions_to_check = getattr(e, 'exceptions', [])
            else:
                # 如果是嵌套的ExceptionGroup，尝试提取错误信息
                exceptions_to_check = [e]
            
            for ex in exceptions_to_check:
                if "Received request before initialization was complete" in str(ex):
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        status_code=503,
                        content={"detail": "MCP server is still initializing, please retry in a moment"},
                        headers={
                            "Content-Type": "application/json",
                            "Retry-After": "2"
                        }
                    )
                elif isinstance(ex, AssertionError) and ('http.response.body' in str(ex) or 'message["type"]' in str(ex)):
                    logger.info("检测到SSE响应格式错误（异常组），通常是客户端断开连接")
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        status_code=499,
                        content={"detail": "Client disconnected during SSE"},
                        headers={"Content-Type": "application/json"}
                    )
                elif "ClientDisconnect" in str(ex):
                    logger.info("异常组中检测到客户端断开连接")
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        status_code=499,
                        content={"detail": "Client disconnected"},
                        headers={"Content-Type": "application/json"}
                    )
            
            # 其他异常组错误 - 大多数情况下也是连接相关问题
            logger.info(f"处理异常组错误，可能是连接问题: {error_msg}")
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=499,
                content={"detail": "Connection error"},
                headers={"Content-Type": "application/json"}
            )
        
        # 处理其他RuntimeError
        elif isinstance(e, RuntimeError):
            logger.error(f"Runtime错误: {e}")
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content={"detail": f"Runtime error: {error_msg}"},
                headers={"Content-Type": "application/json"}
            )
        
        # 其他错误
        else:
            # 检查是否包含连接相关的关键词
            connection_keywords = [
                'http.response.body', 'message["type"]', 'AssertionError', 
                'TaskGroup', 'anyio', 'disconnect', 'connection', 'SSE'
            ]
            
            if any(keyword in error_msg.lower() for keyword in connection_keywords):
                logger.info(f"检测到连接相关错误: {error_msg}")
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    status_code=499,
                    content={"detail": "Connection error"},
                    headers={"Content-Type": "application/json"}
                )
            else:
                logger.error(f"未知服务器错误: {error_msg}")
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {error_msg}"},
                    headers={"Content-Type": "application/json"}
                )

# 禁用自定义错误处理中间件，让uvicorn底层处理，我们已经过滤了日志
# fastapi_cms_final_app.middleware("http")(mcp_error_handler)

# 添加MCP初始化状态跟踪
mcp_initialization_lock = threading.Event()

# 🔥 创建精简版MCP服务 - 专注SQL分析，提升准确率
cms_final_mcp = FastApiMCP(
    fastapi=fastapi_cms_final_app,
    name="CMS企业管理系统",
    description="""
🏢 CMS企业内容管理系统 - 数据导出平台

🎯 核心功能：
- 🔍 名称查询：人员/公司/部门精确搜索
- 🗄️ 数据导出：Job/Booking数据Excel/CSV导出
- 📊 系统监控：健康检查和状态监控

🔒 安全特性：
- 严格的访问控制和权限验证
- 安全的数据查询和导出
- 超时保护和错误处理

🚀 主要优势：
- 专注数据导出功能
- 高效的名称查询
- 多格式导出支持
""",
    auth_config=auth_config,
    describe_all_responses=False,
    describe_full_response_schema=False
)

# 挂载MCP服务到正确的路径
cms_final_mcp.mount(mount_path="/mcp")

logger.warning("已完成优化版MCP服务挂载:")
logger.warning("  - /mcp: CMS企业管理系统 (全功能优化版)")
logger.warning("    * MCP SSE端点: /mcp/sse")
logger.warning("    * MCP Tools端点: /mcp/tools") 
logger.warning("    * MCP Resources端点: /mcp/resources")
logger.warning("    * 主要功能: 搜索、数据导出、AI分析、系统监控")

# 定义graceful shutdown处理器
shutdown_event = asyncio.Event()

async def shutdown_handler():
    """优雅关闭处理器"""
    logger.warning(f"正在关闭CMS企业管理系统服务... 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 利润数据调度器将在FastAPI应用层自动关闭
    logger.warning("利润数据调度器将随FastAPI应用自动关闭")
    
    shutdown_event.set()
    # 给一些时间清理资源，但不要太长
    await asyncio.sleep(0.5)
    logger.warning("CMS企业管理系统服务已关闭")

if __name__ == "__main__":
    # 启动Prometheus metrics服务（处理端口冲突）
    prometheus_port = HTTP_PORT + 100
    try:
        start_http_server(prometheus_port)
        logger.warning(f"Prometheus监控服务启动在 :{prometheus_port}")
    except OSError as e:
        if "Address already in use" in str(e):
            logger.warning(f"Prometheus端口 {prometheus_port} 已被占用，跳过Prometheus启动")
        else:
            logger.error(f"Prometheus启动失败: {e}")
    
    # 显示MCP服务信息
    logger.warning("=== CMS企业管理系统MCP服务器启动信息 ===")
    logger.warning(f"🚀 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.warning(f"🌐 服务地址: http://{HTTP_HOST}:{HTTP_PORT}")
    logger.warning(f"🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询")
    logger.warning(f"📊 MCP连接信息:")
    logger.warning(f"   - SSE连接地址: http://{HTTP_HOST}:{HTTP_PORT}/mcp/sse")
    logger.warning(f"   - Tools端点: http://{HTTP_HOST}:{HTTP_PORT}/mcp/tools")
    logger.warning(f"   - Resources端点: http://{HTTP_HOST}:{HTTP_PORT}/mcp/resources")
    logger.warning(f"📊 主要功能模块:")
    logger.warning(f"   1. 🔍 名称查询 - 人员/公司/部门精确搜索")
    logger.warning(f"   2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出")
    logger.warning(f"   3. 📊 系统监控 - 健康检查和状态监控")
    logger.warning(f"🔒 安全特性:")
    logger.warning(f"   - 访问控制: 严格的权限验证和访问控制")
    logger.warning(f"   - 数据安全: 安全的数据查询和导出")
    logger.warning(f"   - 超时保护: 导出超时保护和错误处理")
    logger.warning(f"   - 认证: MCP Token验证已启用")
    logger.warning(f"⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: {server_config['limit_concurrency']}")
    logger.warning(f"🔧 网络: 连接保持: {server_config['timeout_keep_alive']}s, 队列: {server_config['backlog']}")
    logger.warning("=" * 60)
    
    # 强化初始化流程
    logger.warning("正在初始化MCP服务组件...")
    time.sleep(2)  # 给更多时间进行初始化
    
    # 标记MCP服务已初始化
    mcp_initialization_lock.set()
    logger.warning("MCP服务初始化完成，开始接受连接")
    
    logger.warning("✅ 利润数据周期性调度器将在FastAPI应用启动时自动启动")
    logger.warning("📋 调度功能说明：")
    logger.warning("  - 自动分析job_details和booking_details数据")
    logger.warning("  - 智能变更检测，只保存有变化的数据")
    logger.warning("  - 分层调度：越久远的数据检查频率越低")
    logger.warning("  - 执行窗口：周末07:00-24:00")
    
    # 创建配置（使用动态环境检测）
    config = uvicorn.Config(
        app=cms_final_mcp.fastapi,
        host=HTTP_HOST,
        port=HTTP_PORT,
        log_level="warning",
        reload=False,
        workers=1,
        limit_concurrency=server_config['limit_concurrency'],
        timeout_keep_alive=server_config['timeout_keep_alive'],
        backlog=server_config['backlog'],
        access_log=False,
        proxy_headers=True,
        server_header=False,
        timeout_graceful_shutdown=server_config['timeout_graceful_shutdown'],
        loop="asyncio",
        h11_max_incomplete_event_size=server_config['h11_max_incomplete_event_size'],
        ws_max_size=16777216,
        lifespan="auto",
        # 优化服务器配置
        http="h11",  # 强制使用h11协议
        # 数据导出相关优化
        timeout_notify=600,  # 600秒通知超时，支持长时间数据导出
        use_colors=False,   # 在服务器环境中禁用颜色输出
    )
    
    # 使用信号处理器
    def signal_handler(signum, _):
        logger.warning(f"收到信号 {signum}，开始优雅关闭...")
        # 直接退出，不等待异步任务
        import sys
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        logger.warning("开始启动CMS企业管理系统服务器...")
        server = uvicorn.Server(config)
        server.run()
    except Exception as e:
        logger.error(f"CMS企业管理系统服务器启动失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")